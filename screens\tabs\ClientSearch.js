import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, FlatList, SafeAreaView,ScrollView,ActivityIndicator} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { getClients } from '../../services/api';
import ClientItem from './components/ClientItem';
import RecentSearches from './components/RecentSearches';
import AsyncStorage from '@react-native-async-storage/async-storage';
import FunLoader from './components/FunLoader';
import NoResults from './components/NoResults';
export default function ClientSearch() {

  const [searchQuery, setSearchQuery] = useState('');
  const MAX_RECENT_SEARCHES = 10;
  const [searchResults, setSearchResults] = useState([]);
  const [resultCount, setResultCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [recentSearchesKey, setRecentSearchesKey] = useState(0);

  const saveRecentSearch = async (query) => {
    try {
      
      let recentSearchesJson = await AsyncStorage.getItem(`recentSearchesclients`);
      let recentSearches = [];

      if (recentSearchesJson !== null) {
        recentSearches = JSON.parse(recentSearchesJson);
      }
      
      let updatedSearches = [query, ...recentSearches.filter(item => item !== query)];
     
      updatedSearches = updatedSearches.slice(0, MAX_RECENT_SEARCHES);      
      await AsyncStorage.setItem(`recentSearchesclients`, JSON.stringify(updatedSearches));
      setRecentSearchesKey(prevKey => prevKey + 1);
    } catch (error) {
      console.error(`Erreur lors de la sauvegarde de la recherche récente pour clients:`, error);
    }
  };
  const handleSearch = async (query) => {

    if (!query.trim()) {
      return;
    }
    try {
      setIsLoading(true);
      setHasSearched(true);
      const clients = await getClients(query);
      setSearchResults(clients.clients);
      setResultCount(clients.pagination.total_count);
      saveRecentSearch(query);
      
    } catch (error) {
      console.error('Erreur lors de la recherche de clients:', error);
    }
    finally{
      setIsLoading(false);
    }
  
  };



  return (
    <View className="flex-1 w-full">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false} contentContainerStyle={{ flexGrow: 1 }}>
        <View className="w-full pt-4 pb-20">
          <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mx-0 mb-6">
            <View className="flex-row items-center justify-between mb-4">
              <View className="flex-1">
                <Text className="text-gray-500 text-sm font-medium uppercase tracking-wide">
                  Base clients
                </Text>
                <Text className="text-primary text-2xl font-bold mt-1">
                  Recherche de client
                </Text>
              </View>
              <View className="bg-gray-50 rounded-xl px-3 py-2">
                <Text className="text-gray-600 text-xs font-semibold">
                  {resultCount > 0 ? `${resultCount} résultats` : 'Recherche'}
                </Text>
              </View>
            </View>
            <Text className="text-gray-600 text-sm leading-5 mb-3">
              Trouvez rapidement vos clients par nom ou prénom
            </Text>
            <View className="h-1 bg-secondary w-16 rounded-full" />
          </View>
          <View className="flex-row items-center bg-gray-100 rounded-full overflow-hidden mb-6">
            <TextInput
              className="flex-1 py-3 px-4"
              placeholder="Nom ou prénom du client"
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={() => handleSearch(searchQuery)}
              returnKeyType="search"
            />
            <TouchableOpacity
              className="p-3"
              onPress={() => handleSearch(searchQuery)}
            >
              <MaterialIcons name="search" size={24} color="#666" />
            </TouchableOpacity>
          </View>
  
          <RecentSearches
            entityType="clients"
            onSearchPress={handleSearch}
            refreshKey={recentSearchesKey}
          />
     {isLoading ? (
            <View className="flex-1 w-full mt-10">
              <FunLoader />
            </View>
          ) : (
            <>
              {hasSearched && (
                <View className="mt-6">
                  <Text className="text-sm font-medium mb-2 text-gray-600">
                    {resultCount > 0 ? `${resultCount} Résultats` : ""}
                  </Text>
                  {searchResults.length > 0 ? (
                    <FlatList
                      data={searchResults}
                      renderItem={({ item }) => <ClientItem client={item} />}
                      keyExtractor={(item) => item.ref_lmb}
                      scrollEnabled={false}
                    />
                  ) : (
                    <NoResults
                      type="clients"
                      searchQuery={searchQuery}
                    />
                  )}
                </View>
              )}
              {!hasSearched && (
                <View className="mt-8 flex-1">
                  <View className="flex-1 justify-center items-center">
                    <View className="w-20 h-20 rounded-full bg-blue-50 items-center justify-center mb-4">
                      <MaterialIcons name="person-search" size={40} color="#3B82F6" />
                    </View>
                    <Text className="text-lg font-semibold text-gray-800 mb-2">
                      Recherchez un client
                    </Text>
                    <Text className="text-gray-500 text-center">
                      Saisissez un nom ou prénom pour commencer
                    </Text>
                  </View>
                </View>
              )}
            </>
          )}
        </View>
      </ScrollView>
    </View>
  );
}
