import React, { useEffect, useState, useCallback } from 'react';
import { View, SafeAreaView, ActivityIndicator, ScrollView, Text, Dimensions } from 'react-native';
import Svg, { Line, Circle, Text as SvgText, Polyline } from 'react-native-svg';
import { getSynthese } from '../../services/api';
import StatCard from './components/StatCard';
import FunLoader from './components/FunLoader';

export default function Stats() {
    const [Synthese, setSynthese] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const currentYear = new Date().getFullYear().toString();

    // Obtenir la largeur de l'écran pour le graphique
    const screenWidth = Dimensions.get('window').width;

    const fetchSynteseData = useCallback(async (year) => {
        setLoading(true);
        try {
            const Synthese = await getSynthese(year);
            setSynthese(Synthese);
        } catch (e) {
            setError("Une erreur s'est produite lors de la récupération des données");
        } finally {
            setTimeout(() => {
                setLoading(false);
            }, 900);
        }
    }, []);

    useEffect(() => {
        fetchSynteseData(currentYear);
    }, []);

    const formatNumber = (number) => {

        return parseFloat(number).toLocaleString('fr-FR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // Préparer les données pour le graphique mensuel
    const prepareChartData = () => {
        if (!Synthese?.ca_mensuel) return null;

        const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];
        const data = [];

        // Créer un tableau avec les 12 mois
        for (let i = 1; i <= 12; i++) {
            const monthData = Synthese.ca_mensuel.find(item => item.mois === i);
            data.push(monthData ? parseFloat(monthData.ca_ht) : 0);
        }

        return { labels: months, data: data };
    };

    // Composant graphique simple avec SVG
    const SimpleLineChart = ({ data, labels, width, height }) => {
        if (!data || data.length === 0) return null;

        const maxValue = Math.max(...data);
        const minValue = Math.min(...data);
        const range = maxValue - minValue || 1;

        const chartWidth = width - 80; // Marges
        const chartHeight = height - 60;
        const stepX = chartWidth / (data.length - 1);

        // Créer les points de la ligne
        const points = data.map((value, index) => {
            const x = 40 + index * stepX;
            const y = 40 + chartHeight - ((value - minValue) / range) * chartHeight;
            return `${x},${y}`;
        }).join(' ');

        return (
            <Svg width={width} height={height}>
                {/* Lignes de grille horizontales */}
                {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
                    const y = 40 + chartHeight * (1 - ratio);
                    return (
                        <Line
                            key={index}
                            x1="40"
                            y1={y}
                            x2={40 + chartWidth}
                            y2={y}
                            stroke="#e5e7eb"
                            strokeWidth="1"
                        />
                    );
                })}

                {/* Ligne du graphique */}
                <Polyline
                    points={points}
                    fill="none"
                    stroke="#fb0074"
                    strokeWidth="3"
                />

                {/* Points sur la ligne */}
                {data.map((value, index) => {
                    const x = 40 + index * stepX;
                    const y = 40 + chartHeight - ((value - minValue) / range) * chartHeight;
                    return (
                        <Circle
                            key={index}
                            cx={x}
                            cy={y}
                            r="4"
                            fill="#fb0074"
                            stroke="#ffffff"
                            strokeWidth="2"
                        />
                    );
                })}

                {/* Labels des mois */}
                {labels.map((label, index) => {
                    const x = 40 + index * stepX;
                    return (
                        <SvgText
                            key={index}
                            x={x}
                            y={height - 10}
                            fontSize="10"
                            fill="#6b7280"
                            textAnchor="middle"
                        >
                            {label}
                        </SvgText>
                    );
                })}

                {/* Labels des valeurs */}
                {[0, 0.5, 1].map((ratio, index) => {
                    const value = minValue + (maxValue - minValue) * ratio;
                    const y = 40 + chartHeight * (1 - ratio);
                    return (
                        <SvgText
                            key={index}
                            x="35"
                            y={y + 3}
                            fontSize="10"
                            fill="#6b7280"
                            textAnchor="end"
                        >
                            {Math.round(value / 1000)}k€
                        </SvgText>
                    );
                })}
            </Svg>
        );
    };


    return (
        <View className="flex-1 w-full">
  {loading ? (
    <FunLoader message="Analyse de vos données..." />
  ) : (
    <ScrollView
      className="flex-1"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
    >
        {error ? (
          <Text className="text-red-500 text-center">{error}</Text>
        ) : (
          <>
          <View className="py-4">
            <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mx-0">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-1">
                  <Text className="text-gray-500 text-sm font-medium uppercase tracking-wide">
                    Tableau de bord
                  </Text>
                  <Text className="text-primary text-2xl font-bold mt-1">
                    Vue d'ensemble {currentYear}
                  </Text>
                </View>
                <View className="bg-gray-50 rounded-xl px-3 py-2">
                  <Text className="text-gray-600 text-xs font-semibold">
                    {new Date().toLocaleDateString('fr-FR', {
                      day: '2-digit',
                      month: 'short'
                    })}
                  </Text>
                </View>
              </View>
              <Text className="text-gray-600 text-sm leading-5 mb-3">
                Suivez vos performances commerciales et financières en temps réel
              </Text>
              <View className="h-1 bg-secondary w-16 rounded-full" />
            </View>
          </View>

          {/* Graphique du chiffre d'affaires mensuel */}
          {prepareChartData() && (
            <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-100">
              <Text className="text-lg font-semibold text-primary mb-4">
                Évolution du CA mensuel {currentYear}
              </Text>
              <View className="items-center">
                <SimpleLineChart
                  data={prepareChartData().data}
                  labels={prepareChartData().labels}
                  width={screenWidth - 48}
                  height={220}
                />
              </View>
            </View>
          )}

            <StatCard
              values={[
                {label: "Chiffre d'affaires HT", value: `${Synthese?.ca?.ht ? formatNumber(Synthese.ca.ht) : '0'} €`},
                {label: "Nombre de ventes", value: `${Synthese?.ca?.nb_ventes ? Synthese.ca.nb_ventes : '0'}`},
                {label: "Marge nette", value: `${Synthese?.ca?.marge_nette ? formatNumber(Synthese.ca.marge_nette) : '0'} €`},
                {label: "TVA", value: `${Synthese?.ca?.tva ? formatNumber(Synthese.ca.tva) : '0'} €`},
              ]}
              title={"Bilan de l'année " + currentYear}
              icon={"pricetag"}
            />
            <StatCard 
              title="Documents"
              values={[
                {label: `${(Synthese?.documents?.nbre_cdc_en_cours || '0')}` + " Commandes en cours", value: `${formatNumber(Synthese?.documents?.montant_total_cdc_en_cours || 0)} €`,labelColor:"green"},
                {label: `${Synthese?.documents?.nbre_cdc_en_attente || '0'}` + " Commandes en attente", value: `${formatNumber(Synthese?.documents?.montant_total_cdc_en_attente || 0)} €`,labelColor:"orange"},
                {label: `${Synthese?.documents?.nbre_fac_a_regler || '0'}` + " Factures à règler", value: `${formatNumber(Synthese?.documents?.montant_total_fac_a_regler || 0)} €` ,labelColor:"red"}              ]}
            />
            <StatCard 
              title="Comptes Bancaires"
              values={Synthese?.comptes_bancaires?.flatMap((compte) => [
                {label: compte.lib +" - "+ compte.iban,labelColor:"blue"},
                {label: "Solde", value: `${formatNumber(compte.solde)} ${compte.devise}`},
                {label: "Dernière opération", value: compte.last_date_move},
                
              ]) || []}
            />
          </>
        )}
      
    </ScrollView>
  )}
</View>

    );
}

