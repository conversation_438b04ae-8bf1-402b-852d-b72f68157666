import React, { useEffect, useState, useCallback } from 'react';
import { View, SafeAreaView, ActivityIndicator, ScrollView,Text } from 'react-native';
import { getDocuments, getSynthese } from '../../services/api';
import StatCard from './components/StatCard';
import FunLoader from './components/FunLoader';

export default function Stats() {
    const [Synthese, setSynthese] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const currentYear = new Date().getFullYear().toString();

    const fetchSynteseData = useCallback(async (year) => {
        setLoading(true);
        try {
            const Synthese = await getSynthese(year);
            setSynthese(Synthese);
        } catch (e) {
            setError("Une erreur s'est produite lors de la récupération des données");
        } finally {
            setTimeout(() => {
                setLoading(false);
            }, 900);
        }
    }, []);

    useEffect(() => {
        fetchSynteseData(currentYear);
    }, []);

    const formatNumber = (number) => {
       
        return parseFloat(number).toLocaleString('fr-FR', { 
            minimumFractionDigits: 2,
            maximumFractionDigits: 2 
        });
    }


    return (
        <View className="flex-1 w-full">
  {loading ? (
    <FunLoader message="Analyse de vos données..." />
  ) : (
    <ScrollView
      className="flex-1"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
    >
        {error ? (
          <Text className="text-red-500 text-center">{error}</Text>
        ) : (
          <>
          <View className="py-4">
            <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mx-0">
              <View className="flex-row items-center justify-between mb-4">
                <View className="flex-1">
                  <Text className="text-gray-500 text-sm font-medium uppercase tracking-wide">
                    Tableau de bord
                  </Text>
                  <Text className="text-primary text-2xl font-bold mt-1">
                    Vue d'ensemble {currentYear}
                  </Text>
                </View>
                <View className="bg-gray-50 rounded-xl px-3 py-2">
                  <Text className="text-gray-600 text-xs font-semibold">
                    {new Date().toLocaleDateString('fr-FR', {
                      day: '2-digit',
                      month: 'short'
                    })}
                  </Text>
                </View>
              </View>
              <Text className="text-gray-600 text-sm leading-5 mb-3">
                Suivez vos performances commerciales et financières en temps réel
              </Text>
              <View className="h-1 bg-secondary w-16 rounded-full" />
            </View>
          </View>
            <StatCard 
              values={[
                {label: "Chiffre d'affaires HT", value: `${Synthese?.ca?.ht ? formatNumber(Synthese.ca.ht) : '0'} €`},
                {label: "Nombre de ventes", value: `${Synthese?.ca?.nb_ventes ? Synthese.ca.nb_ventes : '0'}`},
                {label: "Marge nette", value: `${Synthese?.ca?.marge_nette ? formatNumber(Synthese.ca.marge_nette) : '0'} €`},
                {label: "TVA", value: `${Synthese?.ca?.tva ? formatNumber(Synthese.ca.tva) : '0'} €`},
              ]}
              title={"Bilan de l'année " + currentYear}
              icon={"pricetag"}
            />
            <StatCard 
              title="Documents"
              values={[
                {label: `${(Synthese?.documents?.nbre_cdc_en_cours || '0')}` + " Commandes en cours", value: `${formatNumber(Synthese?.documents?.montant_total_cdc_en_cours || 0)} €`,labelColor:"green"},
                {label: `${Synthese?.documents?.nbre_cdc_en_attente || '0'}` + " Commandes en attente", value: `${formatNumber(Synthese?.documents?.montant_total_cdc_en_attente || 0)} €`,labelColor:"orange"},
                {label: `${Synthese?.documents?.nbre_fac_a_regler || '0'}` + " Factures à règler", value: `${formatNumber(Synthese?.documents?.montant_total_fac_a_regler || 0)} €` ,labelColor:"red"}              ]}
            />
            <StatCard 
              title="Comptes Bancaires"
              values={Synthese?.comptes_bancaires?.flatMap((compte, idx) => [
                {label: compte.lib +" - "+ compte.iban,labelColor:"blue"},
                {label: "Solde", value: `${formatNumber(compte.solde)} ${compte.devise}`},
                {label: "Dernière opération", value: compte.last_date_move},
                
              ]) || []}
            />
          </>
        )}
      
    </ScrollView>
  )}
</View>

    );
}

