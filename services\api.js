import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';



const storeToken = async (token) => {
  try {
    await SecureStore.setItemAsync('userToken', token);
  } catch (error) {
    console.error('Erreur lors du stockage du token:', error);
  }
};
const storeBaseURL = async (baseURL) => {
  try {
    await SecureStore.setItemAsync('baseURL', baseURL + '/lmpilot');
    console.log('baseURL sauvegardée:', baseURL + '/lmpilot');
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la base URL:', error);
  }
}

const storeUserEmail = async (email) => {
  try {
    await AsyncStorage.setItem('userEmail', email);
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de l\'email:', error);
  }
}
const checkExistingToken = async () => {
 
  try {
    const token = await getToken();
    const baseURL = await getBaseURL();
  
    if (token && baseURL) {
      const axiosInstance = await createAuthenticatedAxiosInstance();
      const responsetest = await axiosInstance.get('/test');
      return true;
    }
    return false;
  } catch (error) {
    console.error('Erreur lors de la vérification du token:', error);
    await removeToken();

    return false;
  }
};

const getToken = async () => {
  try {
    return await SecureStore.getItemAsync('userToken');
  } catch (error) {
    console.error('Erreur lors de la récupération du token:', error);
    return null;
  }
};


 const getBaseURL = async () => {
  try {
    return await SecureStore.getItemAsync('baseURL');
  } catch (error) {
    console.error('Erreur lors de la récupération de la base URL:', error);
    return null;
  }
};


const removeToken = async () => {
  try {
    await SecureStore.deleteItemAsync('userToken');
  } catch (error) {
    console.error('Erreur lors de la suppression du token:', error);
  }
};



const login = async (baseURL,username, password) => {
  try {
    const response = await axios.post(`${baseURL}/lmpilot/auth`, { username, password });
    if (response.data && response.data.token) {
      await storeToken(response.data.token);
      await storeBaseURL(baseURL);
      await storeUserEmail(username);
      return response.data;
    }
    throw new Error('Token non reçu du serveur');
  } catch (error) {
    console.error('Erreur lors de la connexion:', error.response.data.error);
    throw error;
  }
};

// Fonction pour créer une instance axios avec le token d'authentification
const createAuthenticatedAxiosInstance = async () => {
  const token = await getToken();
  const baseURL = await getBaseURL();
  return axios.create({
    baseURL: baseURL,
    headers: { Authorization: `Bearer ${token}` }
  });
};


const getClients = async (query) => {
  try {
    const axiosInstance = await createAuthenticatedAxiosInstance();
    const response = await axiosInstance.get('/getClients?query=' + query);
    console.log('Clients récupérés:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des clients:', error);
    throw error;
  }
}


const getSynthese = async (year) => {
    const axiosInstance = await createAuthenticatedAxiosInstance();
    const date_debut = `${year}-01-01`;
    const date_fin = `${year}-12-31`;
    const response = await axiosInstance.get('/getSynthese', {
      params: {
        date_debut,
        date_fin
      }
    });
    
    return response.data;
}
const getDocuments = async()=>{
  const axiosInstance = await createAuthenticatedAxiosInstance();
  const response = await axiosInstance.get("/getDocuments");
  return response.data;
}


export { login, getToken, removeToken, checkExistingToken,getBaseURL,getClients,getSynthese,getDocuments, storeUserEmail };