import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const DocumentCard = ({ doc, formatDate, formatNumber, etatLabel }) => {
  // Déterminer la couleur basée sur l'état du document
  const getStateColor = () => {
    switch(doc.id_etat_doc) {
      case 8: return "bg-orange-100 text-orange-700"; // Commande en attente
      case 9: return "bg-blue-100 text-blue-700";     // Commande en cours
      case 18: return "bg-red-100 text-red-700";      // Facture à régler (rouge au lieu de violet)
      default: return "bg-gray-100 text-gray-700";
    }
  };

  // Déterminer l'icône basée sur l'état du document
  const getStateIcon = () => {
    switch(doc.id_etat_doc) {
      case 8: return "pending";         // Commande en attente
      case 9: return "date-range";      // Commande en cours
      case 18: return "receipt-long";   // Facture à régler
      default: return "description";
    }
  };

  const stateColorClass = getStateColor();
  const stateIcon = getStateIcon();
  
  // Couleur de la barre inférieure selon l'état
  const getBarColor = () => {
    switch(doc.id_etat_doc) {
      case 8: return "#ffedd5"; // Orange pâle
      case 9: return "#dbeafe"; // Bleu pâle
      case 18: return "#fee2e2"; // Rouge pâle
      default: return "#f3f4f6";
    }
  };

  // Couleur de l'icône selon l'état
  const getIconColor = () => {
    switch(doc.id_etat_doc) {
      case 8: return "#c2410c"; // Orange foncé
      case 9: return "#1d4ed8"; // Bleu foncé
      case 18: return "#b91c1c"; // Rouge foncé
      default: return "#4b5563";
    }
  };
  
  return (
    <View
      className="mb-4 rounded-xl overflow-hidden bg-white shadow-sm border border-gray-100"
      activeOpacity={0.7}
    >
      <View className="p-5">
        {/* En-tête */}
        <View className="flex-row justify-between items-center mb-3">
          <View className="flex-row items-center">
            <Text className="text-lg font-bold text-gray-800">{doc.ref_doc}</Text>
            {doc.ref_doc_externe && (
              <Text className="ml-2 text-xs text-gray-500">({doc.ref_doc_externe})</Text>
            )}
          </View>
          <Text className="text-xs text-gray-500">{formatDate ? formatDate(doc.date_creation_doc) : doc.date_creation_doc}</Text>
        </View>
        
        {/* État du document */}
        <View className="flex-row items-center mb-4">
          <View className={`px-3 py-1 rounded-full ${stateColorClass.split(' ')[0]} flex-row items-center`}>
            <MaterialIcons name={stateIcon} size={14} color={getIconColor()} />
            <Text className={`text-xs ml-1 font-medium ${stateColorClass.split(' ')[1]}`}>{etatLabel || "État"}</Text>
          </View>
        </View>
        
        {/* Montants */}
        <View className="flex-row justify-between items-end">
          <View>
            <Text className="text-xs text-gray-500 mb-1">Montant HT</Text>
            <Text className="text-sm text-gray-700">{formatNumber ? formatNumber(doc.montant_ht_devise) : doc.montant_ht_devise} €</Text>
          </View>
          <View>
            <Text className="text-xs text-gray-500 mb-1">Montant TTC</Text>
            <Text className="text-base font-semibold text-gray-900">{formatNumber ? formatNumber(doc.montant_ttc_devise) : doc.montant_ttc_devise} €</Text>
          </View>
        </View>
      </View>
      
    </View>
  );
};

export default DocumentCard;
