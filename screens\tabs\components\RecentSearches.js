import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const MAX_RECENT_SEARCHES = 5;

const RecentSearches = ({ entityType, onSearchPress, refreshKey }) => {
  const [recentSearches, setRecentSearches] = useState([]);

  useEffect(() => {
    loadRecentSearches();
  }, [entityType, refreshKey]);

  const loadRecentSearches = async () => {
    try {
      const savedSearches = await AsyncStorage.getItem(`recentSearches${entityType}`);
      
      if (savedSearches !== null) {
        setRecentSearches(JSON.parse(savedSearches));
      }
    } catch (error) {
      console.error(`Erreur lors du chargement des recherches récentes pour ${entityType}:`, error);
    }
  };

  const saveRecentSearch = async (query) => {
    try {
      let updatedSearches = [query, ...recentSearches.filter(item => item !== query)];
      updatedSearches = updatedSearches.slice(0, MAX_RECENT_SEARCHES);
      setRecentSearches(updatedSearches);
    
      await AsyncStorage.setItem(`recentSearches${entityType}`, JSON.stringify(updatedSearches));
    } catch (error) {
      console.error(`Erreur lors de la sauvegarde de la recherche récente pour ${entityType}:`, error);
    }
  };

  const handleRecentSearchPress = (query) => {
    onSearchPress(query);
    // Ne pas sauvegarder ici, c'est fait dans le parent
  };

  const renderRecentSearch = ({ item }) => (
    <TouchableOpacity 
      className="py-4 px-4 bg-gray-50 rounded-full mr-2 mb-2"
      onPress={() => handleRecentSearchPress(item)}
    >
      <Text className="text-gray-600">{item}</Text>
    </TouchableOpacity>
  );

  return (
    <View>
      <Text className="text-sm font-medium mb-2 text-primary">Recherches récentes</Text>
      <FlatList
        data={recentSearches}
        renderItem={renderRecentSearch}
        keyExtractor={(item, index) => `${item}-${index}`}
        horizontal
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

export default RecentSearches;