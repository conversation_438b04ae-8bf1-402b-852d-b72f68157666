import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { getBaseURL, removeToken } from '../../services/api';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Account({ navigation }) {
  const [userInfo, setUserInfo] = useState({
    email: '',
    appUrl: '',
    appName: 'LMB Remote'
  });

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      // Récupérer l'URL de l'application
      const baseURL = await getBaseURL();
      const cleanUrl = baseURL ? baseURL.replace('/lmpilot', '') : '';

      // Extraire le nom de l'application depuis l'URL
      let appName = 'Non disponible';
      if (cleanUrl) {
        try {
          // Extraire la partie entre https:// et .lundimatin.biz
          const match = cleanUrl.match(/https:\/\/([^.]+)\.lundimatin\.biz/);
          if (match && match[1]) {
            appName = match[1];
          }
        } catch (error) {
          console.error('Erreur lors de l\'extraction du nom de l\'application:', error);
        }
      }

      // Récupérer l'email depuis AsyncStorage (s'il a été sauvegardé lors de la connexion)
      const savedEmail = await AsyncStorage.getItem('userEmail');

      setUserInfo({
        email: savedEmail || 'Non disponible',
        appUrl: appName,
      });
    } catch (error) {
      console.error('Erreur lors du chargement des informations utilisateur:', error);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Déconnexion',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeToken();
              await AsyncStorage.removeItem('userEmail');
              // Rediriger vers l'écran de connexion
              navigation.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            } catch (error) {
              console.error('Erreur lors de la déconnexion:', error);
              Alert.alert('Erreur', 'Une erreur est survenue lors de la déconnexion');
            }
          },
        },
      ]
    );
  };



  return (
    <View className="flex-1 bg-gray-50">
      <View className="py-6">
        {/* Bloc principal du compte */}
        <View className="bg-white p-7 mb-6 shadow-lg border-t border-b border-gray-100">
          {/* En-tête du profil */}
          <View className="items-center mb-8">
            <View className="bg-primary p-4 rounded-full mb-4 shadow-lg">
              <MaterialIcons name="person" size={32} color="#FFFFFF" />
            </View>
            <Text className="text-xl font-bold text-gray-900 mb-1">Mon Compte</Text>
            <Text className="text-gray-500 text-sm mb-6">Informations de connexion</Text>
          </View>

          {/* Informations du compte */}
          <View>
            {/* Nom d'utilisateur */}
            <View className="flex-row items-center mb-6">
              <View className="bg-pink-50 p-3 rounded-lg mr-4 items-center justify-center">
                <MaterialIcons name="email" size={22} color="#fb0074" />
              </View>
              <View className="flex-1">
                <Text className="text-gray-600 font-medium text-sm mb-1">Nom d'utilisateur</Text>
                <Text className="text-gray-900 font-semibold text-base">{userInfo.email}</Text>
              </View>
            </View>

            {/* Application LMB */}
            <View className="flex-row items-center mb-8">
              <View className="bg-blue-50 p-3 rounded-lg mr-4 items-center justify-center">
                <MaterialIcons name="language" size={22} color="#000e36" />
              </View>
              <View className="flex-1">
                <Text className="text-gray-600 font-medium text-sm mb-1">Application LMB</Text>
                <Text className="text-gray-900 font-semibold text-base">{userInfo.appUrl}</Text>
              </View>
            </View>

            {/* Bouton de déconnexion */}
            <TouchableOpacity
              className=" rounded-xl p-4 flex-row items-center justify-center mb-6"
              onPress={handleLogout}
              activeOpacity={0.8}
            >
             
              <Text className="text-red-500 font-semibold text-base">Se déconnecter</Text>
            </TouchableOpacity>

            {/* Version de l'application */}
            <View className="items-center pt-4 border-t border-gray-100">
              <Text className="text-gray-500 text-xs mb-1">Version de l'application</Text>
              <Text className="text-gray-700 font-medium">1.0.0</Text>
            </View>
          </View>
        </View>


      </View>
    </View>
  );
}
