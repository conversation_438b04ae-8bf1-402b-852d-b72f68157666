import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { View, FlatList, ActivityIndicator, Text, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { getDocuments } from '../../services/api';
import DocumentCard from './components/DocumentCard';
import PaginationControls from './components/PaginationControls';
import FunLoader from './components/FunLoader';
import NoResults from './components/NoResults';

export default function Documents() {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeFilter, setActiveFilter] = useState('attente'); // 'attente', 'encours', 'facture'
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Nombre d'éléments par page

  // Map des états de document
  const ETATS_DOCUMENT = {
    8: { id: 'attente', label: 'Commande en attente' },
    9: { id: 'encours', label: 'Commande en cours' },
    18: { id: 'facture', label: 'Facture à régler' }
  };

  const fetchDocumentsData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getDocuments();
      if (response && response.documents) {
        // Debug: logger le format des dates reçues
        if (response.documents.length > 0) {
          console.log('Premier document reçu:', {
            ref: response.documents[0].ref_doc,
            date_creation_doc: response.documents[0].date_creation_doc,
            type_date: typeof response.documents[0].date_creation_doc
          });
        }
        // Trier les documents par date de création (du plus récent au plus ancien)
        const sortedDocs = [...response.documents].sort((a, b) => {
          const dateA = new Date(a.date_creation_doc);
          const dateB = new Date(b.date_creation_doc);

          // Si une des dates est invalide, la mettre à la fin
          if (isNaN(dateA.getTime()) && isNaN(dateB.getTime())) return 0;
          if (isNaN(dateA.getTime())) return 1;
          if (isNaN(dateB.getTime())) return -1;

          return dateB - dateA;
        });
        setDocuments(sortedDocs);
      } else {
        throw new Error("Format de données invalide");
      }
    } catch (e) {
      console.error("Erreur lors de la récupération des documents:", e);
      setError("Une erreur s'est produite lors de la récupération des données");
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 900);
    }
  }, []);

  useEffect(() => {
    fetchDocumentsData();
  }, [fetchDocumentsData]);

  // Réinitialiser la page actuelle quand le filtre change
  useEffect(() => {
    setCurrentPage(1);
  }, [activeFilter]);

  // Filtrer les documents en fonction du filtre actif
  const filteredDocuments = useMemo(() => {
    switch (activeFilter) {
      case 'attente':
        return documents.filter(doc => doc.id_etat_doc === 8);
      case 'encours':
        return documents.filter(doc => doc.id_etat_doc === 9);
      case 'facture':
        return documents.filter(doc => doc.id_etat_doc === 18);
      default:
        return [];
    }
  }, [documents, activeFilter]);

  // Paginer les documents filtrés
  const paginatedDocuments = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredDocuments.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredDocuments, currentPage, itemsPerPage]);

  // Calculer le nombre total de pages
  const totalPages = Math.ceil(filteredDocuments.length / itemsPerPage);

  // Compter les documents par catégorie
  const documentCounts = useMemo(() => {
    return {
      attente: documents.filter(doc => doc.id_etat_doc === 8).length,
      encours: documents.filter(doc => doc.id_etat_doc === 9).length,
      facture: documents.filter(doc => doc.id_etat_doc === 18).length,
    };
  }, [documents]);

  const renderHeader = () => (
    <>
      <View className="py-4">
        <View className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mx-0">
          <View className="flex-row items-center justify-between mb-4">
            <View className="flex-1">
              <Text className="text-gray-500 text-sm font-medium uppercase tracking-wide">
                Gestion documentaire
              </Text>
              <Text className="text-primary text-2xl font-bold mt-1">
                Vos documents
              </Text>
            </View>
            <View className="bg-gray-50 rounded-xl px-3 py-2">
              <Text className="text-gray-600 text-xs font-semibold">
                {filteredDocuments.length} docs
              </Text>
            </View>
          </View>
          <Text className="text-gray-600 text-sm leading-5 mb-3">
            Consultez et gérez vos commandes et factures
          </Text>
          <View className="h-1 bg-secondary w-16 rounded-full" />
        </View>
      </View>
       {/* Filtres épurés */}
       <View className="flex-row gap-2 mb-3">
        <TouchableOpacity
          onPress={() => setActiveFilter('attente')}
          className={`flex-1 py-3 px-3 rounded-lg ${
            activeFilter === 'attente' ? 'bg-primary' : 'bg-gray-100'
          }`}
        >
          <Text className={`text-sm font-medium text-center ${
            activeFilter === 'attente' ? 'text-white' : 'text-gray-700'
          }`}>
            Attente ({documentCounts.attente})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setActiveFilter('encours')}
          className={`flex-1 py-3 px-3 rounded-lg ${
            activeFilter === 'encours' ? 'bg-primary' : 'bg-gray-100'
          }`}
        >
          <Text className={`text-sm font-medium text-center ${
            activeFilter === 'encours' ? 'text-white' : 'text-gray-700'
          }`}>
            En cours ({documentCounts.encours})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => setActiveFilter('facture')}
          className={`flex-1 py-3 px-3 rounded-lg ${
            activeFilter === 'facture' ? 'bg-primary' : 'bg-gray-100'
          }`}
        >
          <Text className={`text-sm font-medium text-center ${
            activeFilter === 'facture' ? 'text-white' : 'text-gray-700'
          }`}>
            Factures ({documentCounts.facture})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Pagination sous les filtres */}
      {filteredDocuments.length > 0 && (
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          setCurrentPage={setCurrentPage}
          containerStyle="mb-4 mx-0"
        />
      )}

    </>
  );

  const formatDocumentDate = (dateString) => {
    if (!dateString) return 'Date non disponible';

    try {
      // Essayer plusieurs formats de date
      let date;

      // Si c'est déjà un timestamp (nombre)
      if (typeof dateString === 'number') {
        date = new Date(dateString);
      }
      // Si c'est une chaîne qui ressemble à un timestamp
      else if (typeof dateString === 'string' && /^\d+$/.test(dateString)) {
        date = new Date(parseInt(dateString));
      }
      // Format ISO ou autres formats standards
      else {
        date = new Date(dateString);
      }

      // Vérifier si la date est valide
      if (isNaN(date.getTime())) {
        console.warn('Date invalide reçue:', dateString);
        return 'Date invalide';
      }

      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Erreur lors du formatage de la date:', error, 'Date reçue:', dateString);
      return 'Erreur de date';
    }
  };

  const formatNumber = (number) => {
    return parseFloat(number).toLocaleString('fr-FR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Fonction pour obtenir le libellé d'état d'un document
  const getEtatLabel = (idEtat) => {
    return ETATS_DOCUMENT[idEtat]?.label || "État inconnu";
  };

  if (loading) {
    return (
      <View className="flex-1">
        <FunLoader message="Chargement des documents..." />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <Text className="text-red-500 text-center">{error}</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 w-full">
      <FlatList
        data={paginatedDocuments}
        renderItem={({ item }) => (
          <DocumentCard 
            doc={item} 
            formatDate={formatDocumentDate}
            formatNumber={formatNumber}
            etatLabel={getEtatLabel(item.id_etat_doc)}
          />
        )}
        keyExtractor={(item) => item.ref_doc}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={null}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <NoResults
            type="documents"
            title="Aucun document trouvé"
            subtitle={`Aucun document ne correspond au filtre "${activeFilter === 'attente' ? 'Commande en attente' : activeFilter === 'encours' ? 'Commande en cours' : 'Factures à régler'}"`}
          />
        }
        onEndReachedThreshold={0.1}
        onRefresh={fetchDocumentsData}
        refreshing={loading}
      />
    </View>
  );
}
