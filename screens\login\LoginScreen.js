import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TextInput, TouchableOpacity, ActivityIndicator, KeyboardAvoidingView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { login, getBaseURL } from '../../services/api';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import LmSvg from './logo';
export default function LoginScreen() {
  const navigation = useNavigation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [lienLMB, setLienLMB] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Références pour les champs de saisie
  const emailRef = useRef(null);
  const passwordRef = useRef(null);



  useEffect(() => {
    const fetchStoredBaseURL = async () => {
      try {
        const storedBaseURL = await getBaseURL();

        const baseURL = storedBaseURL ? storedBaseURL.replace('/lmpilot', '') : "https://.lundimatin.biz";
        setLienLMB(baseURL);
        
      } catch (error) {
        console.error('Erreur lors de la récupération de la baseURL:', error);
      }
    };
  
    fetchStoredBaseURL();
  }, []);
  const handleLogin = async () => {
    if (!email.trim() || !password.trim() || !lienLMB.trim()) {
      setErrorMessage('Veuillez remplir tous les champs.');
      return;
    }
    try {
      setErrorMessage('');
      setIsLoading(true);
      const userData = await login(lienLMB,email, password);

      // Ici, vous pouvez naviguer vers l'écran suivant
      navigation.navigate('Home');
    } catch (error) {
      var errorMessage = 'Une erreur inattendue s\'est produite.';
      if (error.response) {
        errorMessage = error.response.data.error;
      }

    } finally {

      // Ajout d'un délai artificiel de 1 seconde pour le loader
      setTimeout(() => {
        if(errorMessage){
          setErrorMessage(errorMessage);
        }
        setIsLoading(false);
      }, 1000);
    }
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar style="auto" />
      <KeyboardAvoidingView
        className="flex-1"
        behavior="padding"
        keyboardVerticalOffset={0}
      >
        <View className="flex-1 justify-center items-center px-8">
          <View className="mb-10">
            <LmSvg />
          </View>

          <View className="w-full">
            <View className="w-full mb-4">
              {errorMessage ? (
              <View className="flex-row items-center">
                <MaterialIcons name="error" size={16} color="black" />
                <Text className="text-primary ml-1 flex-shrink">{errorMessage}</Text>
              </View>
              ) : <Text>{errorMessage}</Text>}
            </View>

            <TextInput
              className="w-full bg-gray-100 text-gray-700 rounded-lg py-3 px-4 mb-4"
              placeholder="Nom de l'application LMB"
              value={lienLMB}
              onChangeText={setLienLMB}
              keyboardType="url"
              autoCapitalize="none"
              editable={!isLoading}
              returnKeyType="next"
              onSubmitEditing={() => emailRef.current?.focus()}
              submitBehavior="submit"
            />

            <TextInput
              ref={emailRef}
              className="w-full bg-gray-100 text-gray-700 rounded-lg py-3 px-4 mb-4"
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              editable={!isLoading}
              returnKeyType="next"
              onSubmitEditing={() => passwordRef.current?.focus()}
              submitBehavior="submit"
            />

            <TextInput
              ref={passwordRef}
              className="w-full bg-gray-100 text-gray-700 rounded-lg py-3 px-4 mb-6"
              placeholder="Mot de passe"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              autoCapitalize="none"
              editable={!isLoading}
              returnKeyType="go"
              onSubmitEditing={handleLogin}
            />

            <TouchableOpacity
              className={`w-full bg-primary rounded-lg py-3 px-4 ${isLoading ? 'opacity-50' : ''}`}
              onPress={handleLogin}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="#ffffff" />
              ) : (
                <Text className="text-white text-center font-bold text-lg">Se connecter</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  );
}

