import React from 'react';
import { View, Text, TouchableOpacity, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>iew } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const ClientItem = ({ client }) => {
  const handleEmailPress = (email) => {
    if (email) {
      Alert.alert(
        "Envoyer un e-mail",
        `Voulez-vous envoyer un e-mail à ${email} ?`,
        [
          {
            text: "Annuler",
            style: "cancel"
          },
          { 
            text: "OK", 
            onPress: () => Linking.openURL(`mailto:${email}`)
          }
        ]
      );
    }
  };

  const handlePhonePress = (phone) => {
    if (phone) {
      Alert.alert(
        "Appeler",
        `Voulez-vous appeler le ${phone} ?`,
        [
          {
            text: "Annuler",
            style: "cancel"
          },
          { 
            text: "OK", 
            onPress: () => Linking.openURL(`tel:${phone}`)
          }
        ]
      );
    }
  };

  const handleAddressPress = (address) => {
    const formattedAddress = `${address.text_adresse}, ${address.code_postal} ${address.ville}`;
    Alert.alert(
      "Ouvrir dans Maps",
      `Voulez-vous ouvrir cette adresse dans Maps ?\n\n${formattedAddress}`,
      [
        {
          text: "Annuler",
          style: "cancel"
        },
        { 
          text: "OK", 
          onPress: () => Linking.openURL(`https://maps.google.com/?q=${encodeURIComponent(formattedAddress)}`)
        }
      ]
    );
  };
  return (
    <View className="bg-white p-6 mb-4 rounded-3xl border border-gray-200">
      <View className="flex-row justify-between items-center mb-4">
        <View className="flex-1">
          <Text className="font-bold text-primary text-xl mb-1">
            {client.nom_complet || <Text className="text-gray-300">Non renseigné</Text>}
          </Text>
          <Text className="text-gray-400 text-xs">
            Réf: {client.ref_lmb || <Text className="text-gray-300">N/A</Text>}
          </Text>
        </View>
      </View>

      {client.coordonnees.map((coordonnee, index) => (
        <View key={coordonnee.id} className="mb-4">
          <Text className="font-semibold text-gray-700 mb-2">{coordonnee.lib}</Text>

          <View className="flex-row items-center mb-2">
            <View className="bg-blue-50 rounded-full p-2 mr-3">
              <MaterialIcons name="phone" size={18} color="#3B82F6" />
            </View>
            <TouchableOpacity onPress={() => handlePhonePress(coordonnee.tel1)} className="flex-1">
              <Text className="text-gray-700">
                {coordonnee.tel1 || <Text className="text-gray-300">Non renseigné</Text>}
              </Text>
            </TouchableOpacity>
          </View>

          {coordonnee.tel2 && (
            <View className="flex-row items-center mb-2">
              <View className="bg-blue-50 rounded-full p-2 mr-3">
                <MaterialIcons name="phone" size={18} color="#3B82F6" />
              </View>
              <TouchableOpacity onPress={() => handlePhonePress(coordonnee.tel2)} className="flex-1">
                <Text className="text-gray-700">{coordonnee.tel2}</Text>
              </TouchableOpacity>
            </View>
          )}

          <View className="flex-row items-center mb-2">
            <View className="bg-green-50 rounded-full p-2 mr-3">
              <MaterialIcons name="email" size={18} color="#10B981" />
            </View>
            <TouchableOpacity onPress={() => handleEmailPress(coordonnee.email)} className="flex-1">
              <Text className="text-gray-700">
                {coordonnee.email || <Text className="text-gray-300">Non renseigné</Text>}
              </Text>
            </TouchableOpacity>
          </View>

          {index < client.coordonnees.length - 1 && <View className="border-b border-gray-200 my-2" />}
        </View>
      ))}
      {client.adresses.length > 0 && (
        <>
        <View className="border-b border-gray-200 my-2" />
          <Text className="font-semibold text-gray-700 my-2">Adresses</Text>
          {client.adresses.map((adresse, index) => (
            <TouchableOpacity key={adresse.id} className="mb-3" onPress={() => handleAddressPress(adresse)}>
              <View className="flex-row items-start">
                <View className="bg-yellow-50 rounded-full p-2 mr-3 mt-1">
                  <MaterialIcons name="location-on" size={18} color="#F59E0B" />
                </View>
                <View className="flex-1">
                  <Text className="font-medium text-gray-700">{adresse.libelle}</Text>
                  <Text className="text-gray-600">
                    {adresse.text_adresse}, {adresse.code_postal} {adresse.ville}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </>
      )}
    </View>
  );
};

export default ClientItem;

