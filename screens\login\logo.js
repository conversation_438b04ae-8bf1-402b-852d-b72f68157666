import * as React from "react"
import Svg, { <PERSON>, Defs, LinearGradient, Stop } from "react-native-svg"
const LmSvg = (props) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={props.width || 342}
    height={props.height || 79}
    viewBox="0 0 342 79"
    fill="none"
    {...props}
  >
    <Path
      fill="#000E36"
      d="m167.64 26.85-3.453 5.009 6.698 4.617 3.453-5.008-6.698-4.618Z"
    />
    <Path
      fill="#000E36"
      d="M290.159 65.543c-1.176 1.498-2.362 3.55-4.241 4.083-.825.23-1.641.048-1.926-.276-.218-.239-.816-1.326.551-5.161 2.903-8.1 6.612-15.941 11.014-23.315l-7.125-3.95a140.603 140.603 0 0 0-11.66 24.68c-.284.782-.768 2.165-1.072 3.825-5.18 3.052-7.371 3.377-8.187 3.377-.816 0-.332-.029-.351-.02-.048-.056-1.281-2.012 1.148-10.398 1.432-4.96 3.852-11.142 7.172-18.373a175.38 175.38 0 0 1 2.04-4.274l7.571.678.673-7.613-4.468-.4c2.343-4.408 4.146-7.508 4.184-7.566l-6.812-3.873c-.161.277-2.761 4.751-5.844 10.685l-9.412-.84-.673 7.613 6.442.582c-.285.591-.56 1.192-.835 1.793-3.482 7.575-6.025 14.119-7.571 19.442-.968 3.32-1.907 7.355-1.793 10.97-2.457 1.651-3.843 2.138-4.459 2.271-.218-.906-.503-3.549 1.024-9.93 1.129-4.79 3.036-10.742 5.655-17.716l-3.283-1.25c-1.774-2.098-3.852-3.539-6.119-4.188-4.231-1.202-9.06.296-12.922 4.007-4.098 3.94-7.419 8.777-9.677 13.966a26.756 26.756 0 0 1-5.882 8.481l-2.239 2.175c1.044-4.045 3.131-10.694 7.248-21.493 1.509-3.93.048-5.733-1.451-6.544-3.321-1.813-6.025 1.364-9.165 5.047-2.087 2.451-4.838 5.924-8.187 10.312a525.747 525.747 0 0 0-5.778 7.718c1.423-5.762 3.472-12.65 5.36-17.592 1.509-3.959-.294-5.647-1.499-6.305-3.387-1.851-6.005 1.335-9.961 6.134-2.477 2.995-5.769 7.23-9.791 12.602-.238.324-.494.648-.731.973l7.609-25.987-7.097-2.68-.967 2.127c-6.603 14.491-13.415 30.9-26.631 39.742-.446.21-.873.382-1.271.497-1.063.295-1.423-.096-1.556-.248-.218-.239-.816-1.327.55-5.161 2.903-8.1 6.613-15.941 11.015-23.315l-7.182-3.95a140.765 140.765 0 0 0-11.66 24.67c-.313.877-.901 2.528-1.176 4.445a16.986 16.986 0 0 1-3.719 2.423c-.513-2.327-.247-5.084.275-7.86 0-.02.019-.029.028-.058h-.019c.323-1.736.759-3.472 1.177-5.132.142-.572.284-1.116.408-1.66a172.952 172.952 0 0 1 1.859-7.107c2.429-8.624 5.436-17.381 9.203-26.787l-7.049-2.862a294.098 294.098 0 0 0-6.518 17.868 11.34 11.34 0 0 0-2.04-.84c-4.307-1.26-9.231.305-13.149 4.197-4.042 4.007-7.334 8.901-9.611 14.157-1.888 4.36-4.582 8.329-7.979 11.639l-.237.229c1.044-4.045 3.131-10.685 7.248-21.493 1.509-3.96-.294-5.648-1.499-6.306-3.386-1.85-6.005 1.336-9.961 6.134-2.476 2.996-5.768 7.231-9.791 12.602-.237.325-.493.649-.73.973l7.608-25.986-7.096-2.68C85.67 52.626 75.159 63.835 70.85 67.717c1.594-6.353 6.48-18.383 11.356-28.495l-6.631-3.701C68.432 46.95 59.56 59.6 53.85 66.03c1.86-7.126 5.996-18.937 10.445-30.289a129.31 129.31 0 0 0 2.685-6.706l-7.077-2.776s-.987 2.423-2.41 6.058c-7.05 16.331-15.787 25.88-21.119 30.612l-.095-.124c-2.628 2.28-5.341 4.637-8.358 5.562-2.154.658-5.114.4-6.366-1.355-.35-.477-.598-1.135-.768-1.955 6.157-5.924 11.413-11.839 15.862-17.849 8.32-11.2 13.558-22.762 15.588-34.371 1.044-5.944-.683-10.513-4.61-12.22-3.312-1.441-7.22-.325-10.75 3.023-5.01 4.76-8.34 10.942-10.958 16.285a170.476 170.476 0 0 0-10.436 26.968c-1.375 4.732-2.59 9.797-2.57 15.092A214.123 214.123 0 0 1 0 72.955l4.687 6.02a235.888 235.888 0 0 0 10.094-8.414c.171.296.351.582.55.868 2.998 4.255 9.06 6 14.772 4.255 1.888-.582 3.586-1.44 5.142-2.433l.029.067c.389-.229 5.19-3.072 11.375-9.788-2.381 9.101-1.414 11.286.133 12.85a4.486 4.486 0 0 0 3.244 1.365c1.224 0 1.338-.134 2.012-.401 2.912-1.183 7.049-5.485 11.347-10.847-1.556 7.346.256 8.939 1.29 9.855 1.451 1.288 4.07 2.26 8.244-.468h.01c.474-.314 6.394-4.312 14.705-16.007l-4.393 15.015 6.727 3.31c5.19-7.26 10.777-14.862 15.55-21.13-4.336 13.909-2.904 16.494-.427 18.144 3.567 2.376 7.238-1.201 8.443-2.384l3.15-3.063c.873 3.11 3.008 5.514 5.778 6.44.882.295 1.746.42 2.59.42 3.311 0 6.119-1.899 7.191-2.624a46.15 46.15 0 0 0 5.323-4.207c.094.496.208.973.351 1.45.872 2.995 2.58 4.274 3.88 4.818a5.972 5.972 0 0 0 2.353.467c1.603 0 2.931-.591 3.482-.83 1.034-.448 2.011-1.002 2.979-1.593.094.124.18.248.294.372 1.679 1.87 4.003 2.871 6.517 2.871 2.515 0 1.803-.124 2.714-.381 3.377-.945 7.419-3.654 9.762-6.516 2.742-2.547 5.218-5.342 7.476-8.328l-3.652 12.469 6.726 3.31c5.617-7.842 11.679-16.094 16.688-22.628a114.107 114.107 0 0 0-.816 3.167c-2.779 11.257-2.647 15.225.522 16.961 3.71 2.032 6.641-2.032 7.609-3.367a631.769 631.769 0 0 1 10.958-14.806c-4.564 14.434-3.131 17.067-.607 18.746.853.572 1.717.801 2.552.801 2.637 0 4.971-2.28 5.891-3.167l3.207-3.13s.009.077.019.106c.844 3.081 3.007 5.437 5.778 6.334a8.227 8.227 0 0 0 2.533.391c3.244 0 6.015-1.812 7.058-2.5a46.596 46.596 0 0 0 5.199-3.977c.446 2.223 1.433 4.083 3.264 5.266 1.195.772 2.486 1.106 3.823 1.106 2.505 0 5.19-1.173 7.751-2.757.152.134.285.277.446.401 1.452 1.107 3.15 1.555 4.962 1.555 3.074 0 6.471-1.297 9.639-2.948.228.353.475.687.769 1.021 1.679 1.87 4.184 2.9 6.821 2.9 2.638 0 1.86-.124 2.78-.382 4.032-1.125 6.916-4.35 8.747-6.687.835-1.059 4.905-6.239 9.838-13.432l-5.018 17.133 6.726 3.31c5.247-7.335 10.892-15.015 15.683-21.32-4.972 16.389-2.382 18.116-.522 19.356 2.381 1.583 5.199.81 8.406-2.309l10.132-9.883-5.284-5.495-7.192 7.002c1.205-5.037 3.681-12.936 7.429-22.761 1.508-3.96-.294-5.648-1.499-6.306-3.387-1.85-6.006 1.335-9.962 6.134-2.476 2.995-5.768 7.23-9.791 12.602-.237.324-.484.648-.73.973l7.609-25.986-7.097-2.681c-8.14 17.81-23.007 36.785-24.667 38.893M22.807 48.983c2.145-7.308 4.82-14.405 7.922-21.34 2.884-6.45 6.072-12.946 10.987-18.098.465-.486 1.034-1.001 1.707-.982.74.028 1.328.725 1.47 1.46.143.734-.037 1.488-.217 2.213-2.818 11.17-7.21 21.13-14.127 30.402-2.647 3.578-5.616 7.127-8.899 10.675.351-1.478.75-2.928 1.157-4.35v.02Zm118.781-4.37c-.114.706-.294 1.393-.389 1.918-.56 3.1-1.508 6.2-2.694 9.12-1.11 2.728-2.77 5.199-4.791 7.326-2.078 2.194-4.611 4.36-7.334 5.676-.455.22-.968.41-1.442.248-.617-.22-.873-.935-.977-1.583-.332-2.156.218-4.427.759-6.506a36.33 36.33 0 0 1 2.021-5.753c1.66-3.72 3.908-7.174 6.584-10.236.076-.095.161-.181.246-.277.949-1.059 1.964-2.089 3.236-2.728 1.186-.591 3.368-1.059 4.392.096.579.648.56 1.678.399 2.69l-.01.01Zm106.305 13.06a20.877 20.877 0 0 1-1.262 2.204c-1.423 2.184-3.197 4.14-5.189 5.819a28.76 28.76 0 0 1-3.15 2.318c-1.1.697-2.03 1.402-3.159 1.612-.19.038-.38.057-.56 0-.304-.095-.493-.381-.626-.677-.522-1.183-.294-2.604-.085-3.835 1.147-7.012 4.885-13.995 10.255-19.156 1.272-1.22 2.979-2.337 4.81-2.299 2.941.067 2.239 3.616 1.812 5.552-.635 2.853-1.518 5.8-2.846 8.462Z"
    />
    <Path
      fill="#000E36"
      d="m294.448 26.86-3.454 5.008 6.859 4.36 3.454-5.008-6.859-4.36Z"
    />
  </Svg>
)
export default LmSvg