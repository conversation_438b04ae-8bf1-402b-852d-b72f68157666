/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./App.{js,jsx,ts,tsx}", "./screens/**/*.{js,jsx,ts,tsx}"],
  safelist: [
    'bg-blue-200',
    'bg-red-200',
    'bg-green-200',
    'bg-orange-200',
    'bg-blue-100',
    'bg-red-100',
    'bg-green-100',
    'bg-orange-100',
    'text-blue-500',
    'text-red-500',
    'text-green-500',
    'text-orange-500',
    'p-2',
    'rounded-xl',
    'mb-1',
    'bg-primary',
    'shadow-md',
    'bg-transparent',
    'bg-orange-500',
    'bg-blue-500',
    'bg-red-500',
    'text-orange-100',
    'text-blue-100',
    'text-red-100',
    'gap-2',
    'min-w-0',
    'bg-gray-50',
    'bg-gray-100',
    'bg-gray-200',
    'text-gray-400',
    'text-gray-600',
    'text-gray-700',
    'border-gray-200',
    'border-gray-100',
    'rounded-2xl',
    'rounded-lg',
    'space-x-1',
    'min-w-[32px]',
    'h-8',
    'transition-all',
    'duration-300'
  ],
  theme: {
    extend: {
      colors: {
        'secondary': '#fb0074',
        'primary': '#000e36',
      },
    },
  },
  plugins: [],
}
