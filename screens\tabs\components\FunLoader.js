import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, Easing } from 'react-native';

const FunLoader = () => {
  const waveValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animation de vague pour les points
    const waveAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(waveValue, {
          toValue: 1,
          duration: 1200,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
        Animated.timing(waveValue, {
          toValue: 0,
          duration: 1200,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: true,
        }),
      ])
    );

    waveAnimation.start();

    return () => {
      waveAnimation.stop();
    };
  }, []);

  return (
    <View className="flex-1 justify-center items-center bg-gray-50">
      <View className="items-center">
        

        {/* Points de chargement en décalé */}
        <View className="flex-row items-center space-x-2">
          {[0, 1, 2].map((index) => (
            <Animated.View
              key={index}
              style={{
                transform: [
                  {
                    translateY: waveValue.interpolate({
                      inputRange: [0, 0.2 + (index * 0.2), 0.4 + (index * 0.2), 1],
                      outputRange: [0, 0, -8, 0],
                      extrapolate: 'clamp'
                    }),
                  }
                ],
                opacity: waveValue.interpolate({
                  inputRange: [0, 0.2 + (index * 0.2), 0.4 + (index * 0.2), 1],
                  outputRange: [0.5, 1, 1, 0.5],
                  extrapolate: 'clamp'
                }),
              }}
              className="w-4 h-4 bg-primary rounded-full"
            />
          ))}
        </View>
      </View>
    </View>
  );
};

export default FunLoader;
