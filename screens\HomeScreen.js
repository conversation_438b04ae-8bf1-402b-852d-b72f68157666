import React, { useState } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LmSvg from './login/logo';
import ClientSearch from './tabs/ClientSearch';
import Documents from './tabs/Documents';
import Stats from './tabs/Stats';
import Account from './tabs/Account';
export default function HomeScreen({ navigation }) {
  const [activeTab, setActiveTab] = useState('stats');
  const insets = useSafeAreaInsets();

  const tabs = [
    { key: 'stats', icon: 'bar-chart', label: 'Statistiques' },
    { key: 'client', icon: 'person-search', label: 'Clients' },
    { key: 'documents', icon: 'receipt', label: 'Documents' },
    { key: 'account', icon: 'person', label: 'Compte' },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'stats':
        return <Stats />;
      case 'client':
        return <ClientSearch />;
      case 'documents':
        return <Documents />;
      case 'account':
        return <Account navigation={navigation} />;
      default:
        return null;
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {/* Header avec logo/titre */}
      <View className="bg-white px-6 pt-4 pb-6 shadow-lg border-b border-gray-100">
        <View className="flex-row items-center justify-between">
          <LmSvg width={150} height={35} />
          <TouchableOpacity className="p-2">
            <MaterialIcons name="notifications-none" size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Contenu principal */}
      <View className="flex-1 px-6 pb-4">
        <View className="flex-1">
          {renderContent()}
        </View>
      </View>

      {/* Barre de navigation professionnelle */}
      <View className="bg-white border-t border-gray-200 shadow-2xl" style={{ paddingBottom: insets.bottom }}>
        <View className="flex-row justify-around items-center py-2">
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab.key}
              className="flex-1 items-center py-3 px-2"
              onPress={() => setActiveTab(tab.key)}
              activeOpacity={0.7}
            >
              <View
                className={
                  activeTab === tab.key
                    ? 'p-2 mb-1 bg-primary shadow-md'
                    : 'p-2 mb-1 bg-transparent'
                }
                style={{ borderRadius: 12 }}
              >
                <MaterialIcons
                  name={tab.icon}
                  size={22}
                  color={activeTab === tab.key ? '#FFFFFF' : '#6B7280'}
                />
              </View>
              <Text className={`text-xs font-medium ${
                activeTab === tab.key
                  ? 'text-primary'
                  : 'text-gray-500'
              }`}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
}

