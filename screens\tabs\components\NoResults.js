import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, Easing } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const NoResults = ({ 
  type = 'clients', 
  searchQuery = '',
  title,
  subtitle,
  suggestions = []
}) => {
  const bounceValue = useRef(new Animated.Value(0)).current;
  const fadeValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Animation d'entrée
    Animated.parallel([
      Animated.timing(fadeValue, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.spring(scaleValue, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Animation de rebond pour l'icône
    const bounceAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(bounceValue, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(bounceValue, {
          toValue: 0,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    bounceAnimation.start();

    return () => bounceAnimation.stop();
  }, []);

  const bounceTranslate = bounceValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -10],
  });

  // Configuration selon le type
  const getConfig = () => {
    switch (type) {
      case 'clients':
        return {
          icon: 'person-search',
          defaultTitle: 'Aucun client trouvé',
          defaultSubtitle: searchQuery 
            ? `Aucun résultat pour "${searchQuery}"` 
            : 'Essayez une autre recherche',
          color: '#3B82F6',
          bgColor: 'bg-blue-50',
          suggestions: [
            '💡 Vérifiez l\'orthographe'
          ]
        };
      case 'documents':
        return {
          icon: 'description',
          defaultTitle: 'Aucun document trouvé',
          defaultSubtitle: 'Aucun document ne correspond aux critères',
          color: '#8B5CF6',
          bgColor: 'bg-purple-50',
          suggestions: [
            '📅 Changez la période',
            '🔄 Modifiez les filtres',
            '📋 Vérifiez le statut',
            '🔍 Élargissez la recherche'
          ]
        };
      default:
        return {
          icon: 'search-off',
          defaultTitle: 'Aucun résultat',
          defaultSubtitle: 'Essayez une autre recherche',
          color: '#6B7280',
          bgColor: 'bg-gray-50',
          suggestions: []
        };
    }
  };

  const config = getConfig();
  const displayTitle = title || config.defaultTitle;
  const displaySubtitle = subtitle || config.defaultSubtitle;
  const displaySuggestions = suggestions.length > 0 ? suggestions : config.suggestions;

  return (
    <Animated.View 
      style={{
        opacity: fadeValue,
        transform: [{ scale: scaleValue }]
      }}
      className="flex-1 justify-center items-center py-12 px-6"
    >
      {/* Icône principale avec animation */}
      <Animated.View
        style={{
          transform: [{ translateY: bounceTranslate }]
        }}
        className={`w-24 h-24 rounded-full ${config.bgColor} items-center justify-center mb-6 shadow-sm`}
      >
        <MaterialIcons 
          name={config.icon} 
          size={48} 
          color={config.color} 
        />
      </Animated.View>

      {/* Titre principal */}
      <Text className="text-xl font-bold text-gray-800 text-center mb-2">
        {displayTitle}
      </Text>

      {/* Sous-titre */}
      <Text className="text-gray-500 text-center mb-6 leading-5">
        {displaySubtitle}
      </Text>

      {/* Suggestions */}
      {displaySuggestions.length > 0 && (
        <View className="w-full max-w-sm">
          <Text className="text-sm font-medium text-gray-700 mb-3 text-center">
            Suggestions :
          </Text>
          <View className="space-y-2">
            {displaySuggestions.map((suggestion, index) => (
              <Animated.View
                key={index}
                style={{
                  opacity: fadeValue,
                  transform: [{
                    translateX: fadeValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [50, 0]
                    })
                  }]
                }}
                className="bg-white rounded-lg p-3 shadow-sm border border-gray-100"
              >
                <Text className="text-gray-600 text-sm text-center">
                  {suggestion}
                </Text>
              </Animated.View>
            ))}
          </View>
        </View>
      )}

      {/* Petit élément décoratif */}
      <View className="mt-8 flex-row space-x-2">
        {[0, 1, 2].map((index) => (
          <Animated.View
            key={index}
            style={{
              opacity: fadeValue,
              transform: [{
                scale: fadeValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 1]
                })
              }]
            }}
            className="w-2 h-2 bg-secondary/30 rounded-full"
          />
        ))}
      </View>
    </Animated.View>
  );
};

export default NoResults;
