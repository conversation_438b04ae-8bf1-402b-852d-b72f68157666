// components/PaginationControls.js
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

const PaginationControls = ({
  currentPage,
  totalPages,
  setCurrentPage,
  containerStyle
}) => {
  if (totalPages <= 1) return null;

  return (
    <View className={`flex-row items-center justify-between py-3 ${containerStyle || ''}`}>
      {/* Bouton Précédent */}
      <TouchableOpacity
        className={`flex-row items-center py-2 px-3 rounded-lg ${
          currentPage === 1 ? 'bg-gray-100' : 'bg-secondary'
        }`}
        onPress={() => currentPage > 1 && setCurrentPage(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <MaterialIcons
          name="chevron-left"
          size={18}
          color={currentPage === 1 ? "#9CA3AF" : "#FFFFFF"}
        />
        <Text className={`text-sm font-medium ml-1 ${
          currentPage === 1 ? 'text-gray-400' : 'text-white'
        }`}>
          Préc.
        </Text>
      </TouchableOpacity>

      {/* Indicateur de page */}
      <Text className="text-gray-600 text-sm font-medium">
        {currentPage} / {totalPages}
      </Text>

      {/* Bouton Suivant */}
      <TouchableOpacity
        className={`flex-row items-center py-2 px-3 rounded-lg ${
          currentPage === totalPages ? 'bg-gray-100' : 'bg-secondary'
        }`}
        onPress={() => currentPage < totalPages && setCurrentPage(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <Text className={`text-sm font-medium mr-1 ${
          currentPage === totalPages ? 'text-gray-400' : 'text-white'
        }`}>
          Suiv.
        </Text>
        <MaterialIcons
          name="chevron-right"
          size={18}
          color={currentPage === totalPages ? "#9CA3AF" : "#FFFFFF"}
        />
      </TouchableOpacity>
    </View>
  );
};

export default PaginationControls;
