import React from 'react';
import { View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const StatCard = ({ title, values, icon }) => (
    <View className="w-full bg-white rounded-2xl p-6 mb-6 border border-gray-200">
        <View className="flex-row items-center justify-between">
            <Text className="text-lg font-semibold text-primary">{title}</Text>
  
        </View>
        {values.map((item, index) => (
            
            <View key={index} className="flex-row items-center justify-between mt-2">
                  {item.labelColor ? (
                <View className={`bg-${item.labelColor}-100 px-2 py-1 rounded-2xl`}>
                <Text className={`text-sm text-${item.labelColor}-500`}>{item.label}</Text>
                </View>
                ) : (
                    <View className={` px-2 py-1`}>
                    <Text className="text-sm text-primary">{item.label}</Text>
                    </View>
                )}
                <Text className="text-sm font-bold text-primary">{item.value}</Text>
            </View>
        ))}
    </View>
);

export default StatCard;
