{"name": "lmpilot", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/blur": "^4.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "axios": "^1.7.9", "expo": "53.0.20", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.0"}, "devDependencies": {"@babel/core": "^7.20.0", "tailwindcss": "^3.3.2"}, "private": true}